export default {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'subject-case': [2, 'never', ['start-case', 'pascal-case', 'upper-case']],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'type-empty': [2, 'never'],
    'jira-task-id': [2, 'always'],
  },
  plugins: [
    {
      rules: {
        'jira-task-id': (parsed) => {
          const { subject } = parsed;
          const jiraPattern = /XDPA-\d+/;
          
          if (!jiraPattern.test(subject)) {
            return [
              false,
              'Commit message must include a JIRA task ID (e.g., XDPA-123). Format: "type: XDPA-123 description"'
            ];
          }
          
          return [true];
        },
      },
    },
  ],
};
