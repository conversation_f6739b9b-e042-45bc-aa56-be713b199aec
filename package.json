{"name": "convx-portal-journey", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "turbo dev", "build": "turbo build", "lint": "turbo lint", "test": "turbo test", "preview": "turbo preview", "prepare": "husky"}, "workspaces": ["apps/*", "packages/*"], "devDependencies": {"turbo": "^2.5.4", "prettier": "^3.4.2", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "husky": "^9.1.7", "lint-staged": "^15.3.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"]}}